import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class RaceService {

  apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  raceList(isPublic = false) {
    if (isPublic){
      return this.http.get(`${this.apiUrl}public-race`);
    }
    return this.http.get(`${this.apiUrl}race`);
  }

  getRace(id) {
    return this.http.get(`${this.apiUrl}race/${id}`);
  }

  getSession(id, isPublic = false) {
    if (isPublic){
      return this.http.get(`${this.apiUrl}public-race-session/${id}`);
    }
    return this.http.get(`${this.apiUrl}race-session/${id}`);
  }

  endRace(id) {
    return this.http.put(`${this.apiUrl}race/${id}/end`, {});
  }

  calculateBestScore(id) {
    return this.http.put(`${this.apiUrl}race/${id}/calculate-best-score`, {});
  }

  exportBestScores(id) {
    return this.http.get(`${this.apiUrl}race/${id}/export-scores`, {
      responseType: 'arraybuffer',
    });
  }

  exportAllScores(id) {
    return this.http.get(`${this.apiUrl}race/${id}/export-all-scores`, {
      responseType: 'arraybuffer',
    });
  }

  getSessionTeam(id) {
    return this.http.get(`${this.apiUrl}session-team?sort[]=score:desc&session_id=${id}`);
  }

  addAllPossibleTeams(sessionId) {
    return this.http.put(`${this.apiUrl}race-session/${sessionId}/add-all-teams`, null);
  }

  updateTeamScore(obj, teamId) {
    return this.http.patch(`${this.apiUrl}session-team/${teamId}`, obj);
  }

  getRaces(obj) {
    let params = new URLSearchParams();
    for (let key in obj) {
      if (obj[key]) {
        params.append(key, obj[key]);
      }
    }
    return this.http.get(`${this.apiUrl}race?${params}`);
  }

  createRace(obj) {
    return this.http.post(`${this.apiUrl}race`, obj);
  }

  updateRace(obj) {
    return this.http.put(`${this.apiUrl}race`, obj);
  }

  deleteRace(id) {
    return this.http.delete(`${this.apiUrl}race/${id}`);
  }


  createSession(obj) {
    if (obj['type'] === 'notSelected') {
      delete obj['type'];
    }
    return this.http.post(`${this.apiUrl}race-session`, obj);
  }

  addTeams(teamIds, sessionId) {
    const obj = { teams: teamIds };
    return this.http.post(`${this.apiUrl}race-session/${sessionId}/add-team`, obj);
  }

  getAllRaceSession() {
    return this.http.get(`${this.apiUrl}race-session`);
  }

  exportScores(sessionId: any) {
    return this.http.get(`${this.apiUrl}race-session/${sessionId}/export-session-team`, {
      responseType: 'arraybuffer',
    });
  }

}
