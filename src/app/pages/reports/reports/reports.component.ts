import { Component, OnInit } from '@angular/core';
import { RaceService } from 'src/app/core/services/race.service';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss']
})
export class ReportsComponent implements OnInit {


  // bread crumb items
  breadCrumbItems: Array<{}>;
  leaderBoard;
  sessionId;
  raceList: any[] = [];
  selectedRaceId: string;
  constructor(private raceService: RaceService, private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: '' }];
    this.route.queryParams.subscribe(params => {
      this.sessionId = params.id;
      this.selectedRaceId = this.sessionId;
      this.getSessionDetail();
    });
    this.getRaceList();
  }

  getRaceList() {
    this.raceService.raceList().subscribe((res: any) => {
      if (res && res.data) {
        this.raceList = res.data;
      }
    });
  }

  onRaceSelect(event: any) {
    const selectedId = event.target.value;
    this.router.navigate([], {
      queryParams: { id: selectedId },
      queryParamsHandling: 'merge',
    });
  }

  getSessionDetail() {
    this.raceService.getSession(this.sessionId).subscribe(data => {
      this.leaderBoard = data['data'].race_teams;
    });
  }

}
