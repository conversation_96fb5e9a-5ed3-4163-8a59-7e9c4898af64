html, body, app-root {
  height: 100%;
}
:host {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
}
header .green-title {
  color: #4eae32;
  //font-weight: 700;
  font-size: 24px;
}

.home-actions {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.home-action-btn {
  width: 100%;
  font-size: 1.2rem;
  padding: 1.1rem 1rem;
  border-radius: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  transition: background 0.2s, color 0.2s;
}
.home-action-btn:last-child {
  margin-bottom: 0;
}
.btn-success {
  background: #4eae32;
  border: none;
  color: #fff;
}
.btn-success:hover {
  background: #388e1f;
  color: #fff;
}
.btn-outline-primary {
  border: 2px solid #2a7be4;
  color: #2a7be4;
  background: #fff;
}
.btn-outline-primary:hover {
  background: #2a7be4;
  color: #fff;
}
.bg-soft-primary {
  background: #e6f0fa;
}
.text-primary {
  color: #2a7be4 !important;
}
.avatar-md {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.profile-user-wid {
  width: 90px;
  height: 90px;
}
.avatar-title {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f4f4f4;
}
.main-center-area {
  flex: 1 0 auto;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
  flex: 1;
  height: calc(100vh - 250px);
}
.modern-card {
  width: 100%;
  max-width: 400px;
  background: #f8fafb;
  border-radius: 1.5rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  border: 1px solid #e6f0fa;
}
@media (max-width: 768px) {
  .modern-card {
    max-width: 400px;
    padding: 1.2rem !important;
  }
  .main-center-area {
    padding: 0 1rem;
  }
}
footer {
  flex-shrink: 0;
  width: 100%;
  margin-top: auto;
}
