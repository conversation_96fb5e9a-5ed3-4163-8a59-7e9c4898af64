<div class="container-fluid">


  <div class="row">
    <div class="col-12">
      <div class="text-center my-3 d-flex justify-content-center align-items-center">
        <a (click)="openModal(content)" class="btn btn-success inner mr-3">
          Start New Evaluation </a>
        <button class="btn btn-info ml-2" (click)="openDdkModal(ddkModal)">
          DDK Sorumluları
        </button>
      </div>
    </div> <!-- end col-->
  </div>

  <app-page-title title="Evaluation" [breadCrumbItems]="breadCrumbItems"></app-page-title>
  <!-- end page title -->
  <div class="row">
    <input #search id="search" type="text" class="form-control col-8" placeholder="Search" aria-label="Search"
        aria-describedby="basic-addon1">

<div class="col-4">
  <label class="form-check form-check-label label">
    <input
      id="id" [(ngModel)]="showType"
      (change)="filterOnEvaluation($event.target.checked)"
      type="checkbox"
      name="filter">
    Only Active</label>
</div>
    <div class="col-12" *ngIf="listed">

      <ngx-datatable [rows]="rows" class="bootstrap" [loadingIndicator]="loadingIndicator" [columnMode]="'force'"
        [headerHeight]="50" [footerHeight]="50" [scrollbarH]="true" [rowHeight]="'auto'" limit="300" [columns]="columns"
        [reorderable]="reorderable"   >
        <ngx-datatable-column *ngFor="let column of columns; let i = index;" name="{{column.name}}"
          prop="{{column.prop}}" href="#ste" [cellClass]="pickCellColor">

          <ng-template let-value="value" let-row="row" ngx-datatable-cell-template >

            <div class="" style="width:90%;" *ngIf="column.prop === 'progress'">
              <ngb-progressbar [value]="value" showValue="true" [striped]="true" [animated]="true" type="success"></ngb-progressbar>
            </div>

            <a *ngIf="column.prop === 'vehicle_number' || column.name === 'Vehicle Number'" target="_blank" href="javascript: void(0);"
            [routerLink]="['/team/detail/', row.team_id]">{{value}}</a>

            <a *ngIf="column.prop === 'Team_name' || column.name === 'Team Name'" href="javascript: void(0);"
              [routerLink]="['/evaluation/', row.id]">{{value}}</a>

            <span *ngIf="!(column.prop === 'team_name' || column.prop === 'progress' || column.prop === 'vehicle_number')">{{value}}</span>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>

    </div>
  </div>
  <!-- end row -->
</div> <!-- container-fluid -->

<ng-template #content let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Start New Evaluation</h5>
    <button type="button" class="close" aria-hidden="true" (click)="closeEventModal()">×</button>
  </div>
  <div class="modal-body p-3">
    <form (ngSubmit)="startEvaluation()" [formGroup]="formData">
      <div class="row">
        <div class="col-12">
          <div class="form-group"><label class="control-label">Team Name</label><select class="form-control"
              name="title" formControlName="title">
              <option value="">Select Team</option>
              <option *ngFor="let option of title;" [value]="option.id"> {{ option.id }} - {{option.team_name}}
              </option>
            </select>
          </div>

        </div>
      </div>

      <div class="text-right pt-4">
        <button type="button" class="btn btn-light" (click)="closeEventModal()">Close</button>
        <button type="submit" [disabled]="formData.controls.title.value === ''" class="btn btn-success save-event ml-1">Start</button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #ddkModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">DDK Sorumluları</h5>
    <button type="button" class="close" aria-hidden="true" (click)="modal.dismiss()">×</button>
  </div>
  <div class="modal-body p-3">
    <input type="text" class="form-control mb-3" placeholder="Arama..." [(ngModel)]="ddkSearchText" (input)="filterDdkList()" />
    <div *ngIf="filteredDdkList && filteredDdkList.length > 0">
      <div class="table-responsive">
        <table class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>Ad</th>
              <th>Soyad</th>
              <th>Email</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let group of filteredDdkList">
              <tr>
                <td colspan="3" class="font-weight-bold text-primary text-left" style="background:#f7f7f7;">
                  {{ group.roleName }}
                </td>
              </tr>
              <tr *ngFor="let user of group.users">
                <td>{{ user.first_name }}</td>
                <td>{{ user.last_name }}</td>
                <td>{{ user.email }}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
    <div *ngIf="filteredDdkList && filteredDdkList.length === 0">
      <p>Veri bulunamadı.</p>
    </div>
  </div>
</ng-template>
