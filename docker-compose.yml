version: '2'

services:
  webbuilder:
    image: node:alpine
    volumes:
      - ".:/app"
    working_dir: /app
    command: sh -c "npm run install-build &&
      rm -rf /app/build/* &&
      mv /app/dist/skote/* /app/build/"
    environment:
      - NODE_OPTIONS=--openssl-legacy-provider
  web:
    image: nginx:latest
    ports:
      - "7085:80"
    volumes:
      - ./build:/app
      - ../ec-backend:/api
      - ./nginx-site.conf:/etc/nginx/conf.d/default.conf
    networks:
      - code-network
    restart: always

  php:
    image: cyberduck/php-fpm-laravel:8.1
    volumes:
      - ./:/app
      - ../ec-backend:/api
    networks:
      - code-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: always

  mysql:
    image : mariadb:10.5.8
    ports:
      - "3306:3306"
    volumes:
      - "../data/mysql:/var/lib/mysql"
    networks:
      - code-network
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: 'true'
      #bind-address   = 0.0.0.0 to mysqld.conf
    tty: true

networks:
  code-network:
    driver: bridge
